package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("修改密码DTO")
public class EmployeePasswordDTD {
//    @ApiModelProperty("用户id")
//    private Long empId;
    @ApiModelProperty("新密码")
    private String newPassword;
    @ApiModelProperty("旧密码")
    private String oldPassword;
}
