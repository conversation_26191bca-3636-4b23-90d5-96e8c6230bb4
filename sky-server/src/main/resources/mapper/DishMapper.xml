<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.DishMapper">

    <resultMap id="DishFlavorMap" type="com.sky.vo.DishVO">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="category_id" property="categoryId" />
        <result column="price" property="price" />
        <result column="image" property="image" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="update_time" property="updateTime" />
        <result column="categoryName" property="categoryName" />
        <collection property="flavors" ofType="com.sky.entity.DishFlavor">
            <result column="flavor_id" property="id" />
            <result column="dish_id" property="dishId" />
            <result column="flavor_name" property="name" />
            <result column="flavor_value" property="value" />
        </collection>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into dish (name, category_id, price, image, description, status, create_time, update_time, create_user, update_user)
        values (#{name},#{categoryId},#{price},#{image},#{description},#{status},#{createTime},#{updateTime},#{createUser},#{updateUser})
    </insert>

    <select id="page" resultType="com.sky.vo.DishVO">
        select d.*, c.name as categoryName from dish d LEFT JOIN category c on d.category_id = c.id
        <where>
            <if test="name != null">
                d.name like concat('%', #{name}, '%')
            </if>
            <if test="categoryId != null">
                and d.category_id = #{categoryId}
            </if>
            <if test="status != null">
                and d.status = #{status}
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="getByIdsAndStatus" resultType="com.sky.entity.Dish">
        select * from dish where status = 1 and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="getDishFlavorByCategoryId" resultMap="DishFlavorMap">
        select d.*, c.name as categoryName, df.id as flavor_id, df.name as flavor_name, df.value as flavor_value
        from dish as d
            left join dish_flavor as df on d.id = df.dish_id
            left join category as c on d.category_id = c.id
        <where>
            <if test="id != null">
                d.id = #{id}
            </if>
            <if test="categoryId != null">
                and d.category_id = #{categoryId}
            </if>
            <if test="status != null">
                and d.status = #{status}
            </if>
        </where>
        order by update_time desc
    </select>

    <delete id="deleteByIds">
        delete from dish where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>
</mapper>