<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.CategoryMapper">
    <update id="update">
        update category
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
        </set>
        where id=#{id}
    </update>

    <select id="page" resultType="com.sky.entity.Category">
        select * from category
        <where>
            <if test="name != null">
                name like concat('%', #{name}, '%')
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
        order by sort desc
    </select>
    <select id="getByType" resultType="com.sky.entity.Category">
        select * from category
            <where>
                <if test="type != null">
                    type = #{type}
                </if>
            </where>
        order by sort desc
    </select>
</mapper>