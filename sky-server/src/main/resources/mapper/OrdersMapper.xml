<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrdersMapper">
    <select id="page" resultType="com.sky.entity.Orders">
        select * from orders
        <where>
            <if test="status != null">
                status = #{status}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
        </where>
        order by order_time desc
    </select>
</mapper>