package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.annotation.AutoFill;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.enumeration.OperationType;
import com.sky.vo.DishVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface DishMapper {

    @AutoFill(OperationType.INSERT)
    void insert(Dish dishEntity);

    Page<DishVO> page(DishPageQueryDTO dishPageQueryDTO);

    @AutoFill(OperationType.UPDATE)
    @Update("update dish set status = #{status}, update_user=#{updateUser}, update_time=#{updateTime} where id = #{id}")
    void updateStatus(Dish dish);

    @Select("select d.*, c.name as categoryName from dish as d left join category as c on d.category_id = c.id where d.id = #{id}")
    DishVO getById(Long id);

    List<Dish> getByIdsAndStatus(List<Long> ids);

    @Select("select * from dish where category_id = #{categoryId}")
    List<Dish> getByCategoryId(Dish dish);

    List<DishVO> getDishFlavorByCategoryId(Dish dish);

    @AutoFill(OperationType.UPDATE)
    @Update("update dish set name = #{name}, price = #{price}, image = #{image}, description = #{description}, status = #{status}, update_time = #{updateTime}, update_user = #{updateUser} where id = #{id}")
    void update(Dish dish);

    void deleteByIds(List<Long> ids);
}
