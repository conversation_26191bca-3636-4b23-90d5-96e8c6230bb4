package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.annotation.AutoFill;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import com.sky.enumeration.OperationType;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface CategoryMapper {
    @AutoFill(OperationType.INSERT)
    @Select("insert into category (type, name, sort, status, create_time, update_time, create_user, update_user) " +
            "values (#{type},#{name},#{sort},#{status},#{createTime},#{updateTime},#{createUser},#{updateUser})")
    public void insert(Category category);

    public Page<Category> page(CategoryPageQueryDTO queryDTO);

    @Delete("delete from category where id=#{id}")
    void delete(Long id);

    @AutoFill(OperationType.UPDATE)
    void update(Category category);

    @Select("select id,name,type,status from category where type=#{type} order by sort desc")
    List<Category> getByType(Integer type);

    @AutoFill(OperationType.UPDATE)
    @Update("update category set status = #{status}, update_time=#{updateTime}, update_user=#{updateUser} where id = #{id}")
    void updateStatus(Category categoty);
}
