package com.sky.controller.admin;

import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.CategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController("adminCategoryController")
@RequestMapping("/admin/category")
@Slf4j
@Api(tags="分类管理")
class CategoryController {

    @Autowired
    private CategoryService categoryService;

    @ApiOperation("新增分类")
    @PostMapping
    public Result addCategory(@RequestBody CategoryDTO categoryDTO) {
        log.info("新增分类：{}", categoryDTO);
        categoryService.create(categoryDTO);
        return Result.success();
    }

    @ApiOperation("查询分类列表")
    @GetMapping("/page")
    public Result<PageResult> page(CategoryPageQueryDTO queryDTO) {
        log.info("查询分类列表：{}", queryDTO);
        PageResult pageResult = categoryService.page(queryDTO);
        return Result.success(pageResult);
    }

    @ApiOperation("删除分类")
    @DeleteMapping
    public Result delete(Long id) {
        log.info("根据id删除分类{}", id);
        categoryService.delete(id);
        return Result.success();
    }

    @ApiOperation("修改分类信息")
    @PutMapping
    public Result update(@RequestBody CategoryDTO categoryDTO) {
        log.info("根据id修改分类信息：{}", categoryDTO);
        categoryService.update(categoryDTO);
        return Result.success();
    }

    @ApiOperation("根据类型查询分类列表")
    @GetMapping("/list")
    public Result<List<Category>> getByType(Integer type) {
        log.info("根据分类类型查询列表：{}", type);
        List<Category> categories = categoryService.getByType(type);
        return Result.success(categories);
    }

    @ApiOperation("修改分类状态")
    @PostMapping("/status/{status}")
    public Result updateStatus(@PathVariable Integer status, Long id) {
        log.info("切换分类状态: {}={}", id, status);
        categoryService.updateStatus(status, id);
        return Result.success();
    }
}
