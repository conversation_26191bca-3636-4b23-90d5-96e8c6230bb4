package com.sky.controller.user;

import com.sky.dto.UserLoginDTO;
import com.sky.result.Result;
import com.sky.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/user/user")
@Api(tags="用户管理接口")
public class UserController {

    @Autowired
    private RedisTemplate redisTemplate;

    @PostMapping("/login")
    public Result login(@RequestBody UserLoginDTO userLoginDTO) {
        log.info("用户微信登录：{}", userLoginDTO);
        HashMap<String, String> wxLoginParams = new HashMap<>();
        wxLoginParams.put("appid", "wxf4e560331edcf14c");
        wxLoginParams.put("secret", "4ddb9196ef5a1aec1c64fc9b366b48e3");
        wxLoginParams.put("grant_type", "authorization_code");
        wxLoginParams.put("js_code", userLoginDTO.getCode());
        String session = HttpClientUtil.doGet("https://api.weixin.qq.com/sns/jscode2session", wxLoginParams);
        redisTemplate.opsForValue().set("user_session", session);
        log.info("用户登录成功: {}", session);
        return Result.success();
    }
}
