package com.sky.controller.user;

import com.sky.entity.Dish;
import com.sky.result.Result;
import com.sky.service.DishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController("userDishController")
@RequestMapping("/user/dish")
@Api(tags="菜品管理接口")
public class DishController {

    @Autowired
    private DishService dishService;

    @Qualifier("redisTemplate")
    @Autowired
    private RedisTemplate redisTempate;

    @ApiOperation("根据id查询菜品列表")
    @GetMapping("/list")
    public Result<List<Dish>> getDishById(Long categoryId) {
        log.info("根据id查询菜品列表：{}", categoryId);
        String cachaKey = "DISH_" + categoryId;
        
        List<Dish> deshes = dishService.getByCategoryId(categoryId);
        return Result.success(deshes);
    }
}
