package com.sky.controller.user;

import com.sky.entity.Category;
import com.sky.result.Result;
import com.sky.service.CategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController("userCategoryController")
@RequestMapping("/user/category")
@Api(tags="分类接口")
public class CategotyController {

    @Autowired
    private CategoryService categoryService;

    /**
     * 查询分类数据
     * @params type
     * return Result
     */
    @ApiOperation("查询分类数据")
    @GetMapping("/list")
    public Result<List<Category>> getByType(Integer type) {
        log.info("查询分类数据：{}", type);
        List<Category> categories = categoryService.getByType(type);
        return  Result.success(categories);
    }
}
