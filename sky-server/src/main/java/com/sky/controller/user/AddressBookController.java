package com.sky.controller.user;

import com.sky.entity.AddressBook;
import com.sky.result.Result;
import com.sky.service.AddressBookService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Address;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/user/addressBook")
public class AddressBookController {

    @Autowired
    private AddressBookService addressBookService;

    @ApiOperation("查询用户所有地址数据")
    @GetMapping("/list")
    public Result<List<AddressBook>> list() {
        log.info("查询用户地址数据");
        List<AddressBook> addressBooks = addressBookService.list();
        return Result.success(addressBooks);
    }

    @ApiOperation("新增地址")
    @PostMapping
    public Result add(@RequestBody AddressBook addressBook) {
        log.info("新增地址：{}", addressBook);
        addressBookService.add(addressBook);
        return Result.success();
    }

    @ApiOperation("根据id查询地址")
    @GetMapping("/{id}")
    public Result<AddressBook> getById(@PathVariable Long id) {
        log.info("根据id查询地址：{}", id);
        AddressBook addressBook = addressBookService.getById(id);
        return Result.success(addressBook);
    }

    @ApiOperation("更新地址信息")
    @PutMapping
    public Result update(@RequestBody AddressBook addressBook) {
        log.info("更新地址信息：{}", addressBook);
        addressBookService.update(addressBook);
        return Result.success();
    }

    @ApiOperation("设置默认地址")
    @PutMapping("/default")
    public Result setDefault(@RequestBody AddressBook addressBook) {
        log.info("设置默认地址", addressBook);
        addressBookService.setDefault(addressBook);
        return Result.success();
    }

    @GetMapping("/default")
    public Result<AddressBook> getDefault() {
        log.info("获取默认地址");
        AddressBook addressBook = addressBookService.getDefault();
        return Result.success(addressBook);
    }

    @ApiOperation("删除地址")
    @DeleteMapping
    public Result delete(Long id) {
        log.info("根据id删除地址");
        addressBookService.delete(id);
        return Result.success();
    }
}
