package com.sky.controller.user;

import com.sky.dto.OrdersPageDTO;
import com.sky.dto.OrdersSubmitDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.OrderService;
import com.sky.vo.OrderSubmitVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController("userOrderController")
@RequestMapping("/user/order")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @PostMapping("/submit")
    public Result submit(@RequestBody OrdersSubmitDTO ordersSubmitDTO) {
        log.info("创建订单：", ordersSubmitDTO);
        OrderSubmitVO orderSubmitVO = orderService.submit(ordersSubmitDTO);
        return Result.success(orderSubmitVO);
    }

    @GetMapping("/historyOrders")
    public Result<PageResult> page(OrdersPageDTO ordersPageDTO) {
        log.info("查询历史订单列表: {}", ordersPageDTO);
        PageResult pageResult = orderService.page(ordersPageDTO);
        return Result.success(pageResult);
    }

//    @GetMapping("/orderDetail/#{id}")
//    public Result orderDetail(@PathVariable Long id) {
//
//    }
}
