package com.sky.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.sky.constant.MessageConstant;
import com.sky.dto.UserLoginDTO;
import com.sky.entity.User;
import com.sky.exception.LoginFailedException;
import com.sky.mapper.UserMapper;
import com.sky.service.UserService;
import com.sky.service.WechatService;
import com.sky.vo.UserLoginVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private WechatService wechatService;

    @Autowired
    private UserMapper userMapper;

    public User wxLogin(UserLoginDTO userLoginDTO) {
        JSONObject wxLoginResult = wechatService.wxLogin(userLoginDTO.getCode());
        String openId = wxLoginResult.getString("openid");
        if(openId == null) {
            throw new LoginFailedException(MessageConstant.LOGIN_FAILED);
        }
        //通过openid查询用户是否注册
        User user = userMapper.getByOpenId(openId);
        if(user == null) {
            user = User.builder()
                    .openid(openId)
                    .name(userLoginDTO.getName())
                    .avatar(userLoginDTO.getAvatar())
                    .createTime(LocalDateTime.now())
                    .build();
            userMapper.insert(user);
        }
        return user;
    }
}
