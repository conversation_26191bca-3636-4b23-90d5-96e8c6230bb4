package com.sky.service.impl;

import com.sky.constant.JwtClaimsConstant;
import com.sky.constant.MessageConstant;
import com.sky.context.BaseContext;
import com.sky.entity.AddressBook;
import com.sky.exception.AddressBookBusinessException;
import com.sky.mapper.AddressBookMapper;
import com.sky.service.AddressBookService;
import okhttp3.Address;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

@Service
public class AddressBookServiceImpl implements AddressBookService {

    @Autowired
    private AddressBookMapper addressBookMapper;

    public List<AddressBook> list() {
        HashMap threadLocal = BaseContext.getCurrentId();
        Long userId = (Long) threadLocal.get(JwtClaimsConstant.USER_ID);
        AddressBook addressBook = new AddressBook();
        addressBook.setUserId(userId);
        List<AddressBook> addressBooks = addressBookMapper.list(addressBook);
        return addressBooks;
    }

    public void add(AddressBook addressBook) {
        HashMap threadLocal = BaseContext.getCurrentId();
        Long userId = (Long) threadLocal.get(JwtClaimsConstant.USER_ID);
        addressBook.setUserId(userId);
        addressBook.setIsDefault(0);
        addressBookMapper.insert(addressBook);
    }

    public AddressBook getById(Long id) {
        AddressBook addressBook = addressBookMapper.getById(id);
        return addressBook;
    }

    public void update(AddressBook addressBook) {
        addressBookMapper.update(addressBook);
    }

    @Transactional
    public void setDefault(AddressBook addressBook) {
        HashMap threadLocal = BaseContext.getCurrentId();
        Long userId = (Long) threadLocal.get(JwtClaimsConstant.USER_ID);
        AddressBook ordinaryAddressBook = new AddressBook();
        ordinaryAddressBook.setIsDefault(0);
        ordinaryAddressBook.setUserId(userId);
        addressBook.setUserId(userId);
        addressBook.setIsDefault(1);
        addressBookMapper.update(ordinaryAddressBook);
        addressBookMapper.update(addressBook);
    }

    @Override
    public AddressBook getDefault() {
        HashMap threadLocal = BaseContext.getCurrentId();
        Long userId = (Long) threadLocal.get(JwtClaimsConstant.USER_ID);
        AddressBook addressBook = AddressBook.builder()
                                    .userId(userId)
                                    .isDefault(1)
                                    .build();
        List<AddressBook> list = addressBookMapper.list(addressBook);
        if(list != null && list.size() > 0) {
            return list.get(0);
        } else {
            throw new AddressBookBusinessException(MessageConstant.ADDRESS_BOOK_IS_NULL);
        }
    }

    public void delete(Long id) {
        HashMap threadLocal = BaseContext.getCurrentId();
        Long userId = (Long) threadLocal.get(JwtClaimsConstant.USER_ID);
        AddressBook addressBook = new AddressBook();
        addressBook.setUserId(userId);
        addressBook.setId(id);
        addressBookMapper.delete(addressBook);
    }
}
