package com.sky.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sky.properties.WeChatProperties;
import com.sky.service.WechatService;
import com.sky.utils.HttpClientUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
public class WechatServiceImpl implements WechatService {

    final String WECHAT_LOGIN_URI = "https://api.weixin.qq.com/sns/jscode2session";

    @Autowired
    private WeChatProperties weChatProperties;

    public JSONObject wxLogin(String code) {
        HashMap<String, String> wxLoginParams = new HashMap<>();
        wxLoginParams.put("appid", weChatProperties.getAppid());
        wxLoginParams.put("secret", weChatProperties.getSecret());
        wxLoginParams.put("grant_type", "authorization_code");
        wxLoginParams.put("js_code", code);
        String session = HttpClientUtil.doGet(WECHAT_LOGIN_URI, wxLoginParams);
        JSONObject result = JSON.parseObject(session);
        return result;
    }
}
