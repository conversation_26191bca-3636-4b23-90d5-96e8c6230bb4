package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.annotation.AutoFill;
import com.sky.constant.MessageConstant;
import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.entity.DishFlavor;
import com.sky.entity.SetmealDish;
import com.sky.enumeration.OperationType;
import com.sky.exception.DeletionNotAllowedException;
import com.sky.mapper.DishFlavorMapper;
import com.sky.mapper.DishMapper;
import com.sky.mapper.SetmealDishMapper;
import com.sky.result.PageResult;
import com.sky.service.DishService;
import com.sky.vo.DishVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class DishServiceImpl implements DishService {

    @Autowired
    private DishMapper dishMapper;

    @Autowired
    private DishFlavorMapper dishFlavorMapper;

    @Autowired
    private SetmealDishMapper setmealDishMapper;

    @Transactional
    public void addDish(DishDTO dish) {
        Dish dishEntity = new Dish();
        BeanUtils.copyProperties(dish, dishEntity);
        dishMapper.insert(dishEntity);
        Long dishId = dishEntity.getId();
        List<DishFlavor> dishFlavors = dish.getFlavors();
        for (DishFlavor dishFlavor : dishFlavors) {
            dishFlavor.setDishId(dishId);
        }
        dishFlavorMapper.insert(dishFlavors);
        log.info("新增菜品id:{}", dishEntity.getId());
    }

    public PageResult page(DishPageQueryDTO dishPageQueryDTO) {
        PageHelper.startPage(dishPageQueryDTO.getPage(), dishPageQueryDTO.getPageSize());
        Page<DishVO> page = dishMapper.page(dishPageQueryDTO);
        PageResult pageResult = new PageResult(page.getTotal(), page.getResult());
        return pageResult;
    }

    public void updateStatus(Integer status, Long id) {
        Dish dish = Dish.builder().id(id).status(status).build();
        dishMapper.updateStatus(dish);
    }

    public DishVO getById(Long id) {
        DishVO dish = dishMapper.getById(id);
        List<DishFlavor> flavors = dishFlavorMapper.getByDishId(id);
        dish.setFlavors(flavors);
        return dish;
    }

    @Transactional
    public void deleteByIds(List<Long> ids) {
        if(ids == null || ids.size() < 1) {
            return;
        }
        //起售中的菜品无法删除
        List<Dish> dishs = dishMapper.getByIdsAndStatus(ids);
        if(dishs.size() > 0) {
            log.info("起售中菜品无法删除");
            throw new DeletionNotAllowedException(MessageConstant.DISH_ON_SALE);
        }
        //套餐中的菜品无法删除
        List<SetmealDish> setmealDishes = setmealDishMapper.getByDishIds(ids);
        if(setmealDishes.size() > 0) {
            log.info("套餐中的菜品无法删除");
            throw new DeletionNotAllowedException(MessageConstant.DISH_BE_RELATED_BY_SETMEAL);
        }
        //删除菜品数据
        dishMapper.deleteByIds(ids);
        //关联口味一起删除
        dishFlavorMapper.deleteByDishIds(ids);
    }

    /**
     * 根据分类id查询菜品
     * @param categoryId
     * @return List<Dish>
     */
    public List<Dish> getByCategoryId(Long categoryId) {
       List<Dish> dishs = dishMapper.getByCategoryId(categoryId);
       return dishs;
    }

    @Transactional
    public void update(DishDTO dishDTO) {
        //更新菜品
        Dish dish = new Dish();
        BeanUtils.copyProperties(dishDTO, dish);
        dishMapper.update(dish);
        //删除口味
        dishFlavorMapper.deleteByDishId(dishDTO.getId());
        //重新插入口味
        List<DishFlavor> flavors = dishDTO.getFlavors();
        for (DishFlavor flavor : flavors) {
            flavor.setDishId(dishDTO.getId());
        }
        dishFlavorMapper.insert(flavors);
    }
}
