package com.sky.service;

import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.result.PageResult;
import com.sky.vo.DishVO;

import java.util.List;

public interface DishService {
    void addDish(DishDTO dish);

    PageResult page(DishPageQueryDTO dishPageQueryDTO);

    void updateStatus(Integer status, Long id);

    DishVO getById(Long id);

    void deleteByIds(List<Long> ids);

    List<Dish> getByCategoryId(Long categoryId);

    void update(DishDTO dishDTO);

    List<DishVO> listWithFlavor(Dish dish);
}
