package com.sky.service;


import com.github.pagehelper.Page;
import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import com.sky.result.PageResult;

import java.util.List;

public interface CategoryService {
    void create(CategoryDTO categoryDTO);

    PageResult page(CategoryPageQueryDTO queryDTO);

    void delete(Long id);

    void update(CategoryDTO categoryDTO);

    List<Category> getByType(Integer type);

    void updateStatus(Integer status, Long id);
}
