package com.sky.context;

import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
public class BaseContext {

    private static ThreadLocal<HashMap> threadLocal = new ThreadLocal<>();

    public static void setCurrentId(HashMap map) {
        threadLocal.set(map);
    }

    public static HashMap getCurrentId() {
        return threadLocal.get();
    }

    public static void removeCurrentId() {
        threadLocal.remove();
    }

}
